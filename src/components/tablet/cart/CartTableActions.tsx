import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, I18nManager, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Row } from 'react-native-col';
import { Menu, MenuOption, MenuOptions, MenuTrigger, renderers } from 'react-native-popup-menu';
import { SearchInput } from '../../common';
import { Close, Dots, DraftsInactive, RightArrows } from '../../../assets/svgs/icons';
import { colors, fonts } from '../../../utils/theme';
import { ErrorDialog, OrderClearModal, OrderSuccessModalOffline, SaveDraftConfirmModal } from '../../modals';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../constants';
import { useAppSelector } from '../../../redux/hooks';
import {
	getBranchId,
	getCustomerExternalId,
	getCustomerLegalName,
	getCustomerName,
	getCustomerUserRoleId,
	getPrimaryContactName,
	getSalesPersonName,
	getSalesPersonRoleId,
	getTenantCountry,
	getUserType
} from '../../../redux/selectors';
import { placeOrder, removeAllCartItems, saveToDraft } from '../../../redux/apis/cart';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { clearCartList, setCartSearchedProducts, setShippingNumberDetails } from '../../../redux/features/cart-slice';
import { ShippingNumber } from './ShippingNumber';
import { checkBadRequest } from '../../../utils/helpers';
import { setActiveCustomerShippingNumberDetails } from '../../../redux/features/customer-slice';

const CartTableActions = () => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<any>();
	const userType = useAppSelector(getUserType);
	const customerName = useAppSelector(getCustomerName);
	const tenantCountry = useAppSelector(getTenantCountry);
	const customerLegalName = useAppSelector(getCustomerLegalName);
	const salePersonName = useAppSelector(getSalesPersonName);
	const primaryContactName = useAppSelector(getPrimaryContactName);
	const customerExternalId = useAppSelector(getCustomerExternalId);
	const customerUserRoleId = useAppSelector(getCustomerUserRoleId);
	const salesPersonRoleId = useAppSelector(getSalesPersonRoleId);
	const branchId = useAppSelector(getBranchId);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const shippingDetails = useAppSelector(state => state.cart.shippingDetails);
	const cartProducts = useAppSelector(state => state.cart.cartProducts);
	const savingToDraft = useAppSelector(state => state.cart.savingToDraft);
	const activeCustomer = useAppSelector(state => state.customer.activeCustomer);
	const remarks = useAppSelector(state => state.cart.remark);

	const [showSuccessModal, setShowSuccessModal] = useState(false);
	const [showRemoveModal, setshowRemoveModal] = useState(false);
	const [loading, setLoading] = useState(false);
	const [removeAllLoader, setRemoveAllLoader] = useState(false);
	const [showDraftAdd, setShowDraftAdd] = useState(false);
	const [searchKey, setSearchKey] = useState('');
	const [addressError, setAddressError] = useState(false);
	const [showShippingModal, setShowShippingModal] = useState(false);

	useEffect(() => {
		if (searchKey) {
			const searchedValue = searchKey.toLowerCase();
			const filteredProducts = cartProducts.filter((x) => {
				// console.log('match', x.value.toLowerCase(), x.value.toLowerCase().includes(searchedValue));
				return (
					x.product_name.toLowerCase().includes(searchedValue) || x.product_item_number.toLowerCase().includes(searchedValue)
				);
			});
			dispatch(setCartSearchedProducts({ searchKey, filteredProducts }));
		} else {
			dispatch(setCartSearchedProducts({ searchKey: '', filteredProducts: [] }));
		}
	}, [searchKey]);

	const toggleAddressError = () => {
		setAddressError(!addressError);
	};

	const toggleSuccessModal = () => {
		setShowSuccessModal(!showSuccessModal);
	};
	const handleRemoveModal = () => {
		setshowRemoveModal(!showRemoveModal);
	};
	const onClose = () => {
		setShowSuccessModal(!showSuccessModal);
		dispatch(clearCartList());
		navigation.goBack();
	};

	const removeAllItemFromCart = async () => {
		//console.log('removeAllItemFromCart');
		setRemoveAllLoader(true);
		let cartItemIds: any = [];
		for (let i = 0; i < cartProducts.length; i++) {
			cartItemIds = cartItemIds.concat(cartProducts[i]._id);
		}
		//console.log('cartItemIds', cartItemIds);
		const requestBody: any = {
			cartItemIds,
			tenantId: currentRole?.tenant_id?._id,
			customerUserRoleId
		};
		const response = await dispatch(removeAllCartItems(requestBody));
		if (!response.error) {
			setRemoveAllLoader(false);
			dispatch(clearCartList());
		}
		else {
			setRemoveAllLoader(false);
			Alert.alert(t(checkBadRequest(response.payload)));
			//Alert.alert('Something went wrong');
		}
		handleRemoveModal;
	};

	const toggleSaveDraftModal = () => {
		if (!shippingDetails) {
			toggleAddressError();
			return;
		}
		setShowDraftAdd(!showDraftAdd);
	};

	const onSaveDraftConfirm = async () => {
		const requestBody: any = {
			customerName,
			salesPersonRoleId,
			customerUserRoleId,
			tenantId: currentRole?.tenant_id?._id,
			customerId: activeCustomer?.customer_id,
			customerPrimaryContactName: customerName
		};

		if (activeCustomer?.external_id) {
			requestBody.externalId = activeCustomer?.external_id;
		}

		const response = await dispatch(saveToDraft(requestBody));
		if (!response.error) {
			setShowDraftAdd(false);
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const onSetShippingNumber = (shippingMobileNumber: string) => {
		dispatch(setShippingNumberDetails({
			shippingCountryCode: tenantCountry.country_code,
			shippingMobileNumber
		}));
		dispatch(setActiveCustomerShippingNumberDetails({
			shippingCountryCode: tenantCountry.country_code,
			shippingMobileNumber
		}));
		setShowShippingModal(false);
		setLoading(false);
	};

	const handlePlaceOrder = async () => {
		const shippingMobileNumber = shippingDetails.shippingMobileNumber;
		if (shippingMobileNumber === undefined || shippingMobileNumber === null || shippingMobileNumber === '') {
			setShowShippingModal(true); // Enter shipping mobile number first before place order
			return;
		}

		setLoading(true);
		try {
			let requestBody: any = {
				tenantId: currentRole?.tenant_id?._id,
				salesPersonRoleId: salesPersonRoleId,
				customerUserRoleId: customerUserRoleId,
				customerName: customerName,
				salePersonName: salePersonName,
				branchId: branchId,
				orderPunchDeviceType: 'TABLET',
				orderPunchDeviceOs: Platform.OS.toUpperCase(),
				orderAppType: userType,
				ShippingAddress: shippingDetails.shippingAddress,
				cityId: shippingDetails.shippingCityId,
				regionId: shippingDetails.shippingRegionId,
				shippingMobileNumber: shippingDetails.shippingMobileNumber,
				shippingCountryCode: shippingDetails.shippingCountryCode,
				regionName: shippingDetails.region,
				cityName: shippingDetails.city,
				shippingCoordinates: {
					lat: shippingDetails?.latitude,
					lng: shippingDetails?.longitude
				},
				customerPrimaryContactName: primaryContactName,
				externalId: customerExternalId,
				customer_legal_name: customerLegalName,
				apiVersion: 2
			};
			if (remarks) { requestBody.orderRemark = remarks; }
			const response = await dispatch(placeOrder(requestBody));
			//console.log('response of place order', response);
			setLoading(false);
			if (!response.error) {
				toggleSuccessModal();
				dispatch(clearCartList());
			} else {
				Alert.alert(t(checkBadRequest(response.payload)));
			}
		} catch (error) {
			console.log('In catch', error);
			Alert.alert('Something went wrong!');
			setLoading(false);
		}
	};

	const onClearSearch = () => {
		setSearchKey('');
		dispatch(setCartSearchedProducts({ searchKey: '', filteredProducts: [] }));
	};

	return (
		<Row.LR style={styles.container}>
			<Text style={styles.title}>{t('cart')}</Text>
			<Row style={styles.rightItems}>
				<SearchInput
					value={searchKey}
					placeholder={t('navbar_search_placeholder')}
					inputContainerStyle={styles.searchInputContainer}
					inputStyle={styles.searchInput}
					numberOfLines={1}
					onChangeText={setSearchKey}
					onClear={onClearSearch}
				/>
				<TouchableOpacity
					style={[
						styles.confirmBtn,
						loading && loading && styles.loading,
						shippingDetails === null && styles.confirmBtnDisabled
					]}
					onPress={handlePlaceOrder}
					disabled={shippingDetails === null || loading}
				>
					{
						loading
							? <ActivityIndicator color={colors.white} /> :
							<>
								<Text style={styles.confirmText}>{t('confirm_order')}</Text>
								<RightArrows stroke={colors.white} style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
							</>
					}
				</TouchableOpacity>
				<Menu
					renderer={renderers.Popover}
					rendererProps={{
						placement: 'auto',
						preferredPlacement: 'bottom',
						anchorStyle: styles.menuAnchorStyle
					}}
				>
					<MenuTrigger customStyles={triggerStyles}>
						<View style={styles.optionBtn}>
							<Dots fill={colors.primary} />
						</View>
					</MenuTrigger>
					<MenuOptions customStyles={optionsStyles}>
						<MenuOption value={1} style={styles.menuOption} onSelect={toggleSaveDraftModal}>
							<DraftsInactive />
							<Text style={styles.menuOptionText}>{t('save_as_draft')}</Text>
						</MenuOption>
						<MenuOption value={2} style={styles.menuOption} onSelect={handleRemoveModal}>
							<Close width={20} height={20} fill={colors.primary} />
							<Text style={styles.menuOptionText}>{t('clear_order')}</Text>
						</MenuOption>
					</MenuOptions>
				</Menu>
			</Row>
			<OrderSuccessModalOffline
				isVisible={showSuccessModal}
				onCancel={onClose}
			/>
			<OrderClearModal
				isVisible={showRemoveModal}
				onCancel={handleRemoveModal}
				onConfirm={removeAllItemFromCart}
				loading={removeAllLoader}
				title={t('cart_remove_all_confirm')}
			/>
			<SaveDraftConfirmModal
				isVisible={showDraftAdd}
				heading={t('confirm')}
				title={t('draft_save_confirm')}
				confirmButtonTitle={t('confirm')}
				onCancel={toggleSaveDraftModal}
				onConfirm={onSaveDraftConfirm}
				loading={savingToDraft}
			/>
			<ErrorDialog
				isVisible={addressError}
				title={t('save_draft_address_error')}
				onCancel={toggleAddressError}
			/>
			<ShippingNumber
				isVisible={showShippingModal}
				onConfirm={onSetShippingNumber}
				countryCode={tenantCountry.country_code}
				onBackdropPress={() => setShowShippingModal(false)}
			/>
		</Row.LR>
	);
};

const optionsStyles = {
	optionsContainer: {
		backgroundColor: colors.white,
		borderRadius: 10,
		paddingVertical: 12,
		width: 200
	},
	optionsWrapper: {
	},
	optionWrapper: {
		paddingVertical: 12,
		paddingHorizontal: 16
	},
	optionTouchable: {
		activeOpacity: 1
	}
};

const triggerStyles = {
	triggerOuterWrapper: {
		marginLeft: 10
	},
	TriggerTouchableComponent: TouchableOpacity
};

const styles = StyleSheet.create({
	container: {
		marginTop: VERTICAL_DIMENS._16,
		// marginHorizontal: 30,
		paddingHorizontal: 30,
		backgroundColor: colors.grey100,
		marginBottom: VERTICAL_DIMENS._16
	},
	title: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	orderNumber: {
		color: colors.secondary
	},
	rightItems: {
		alignItems: 'center'
	},
	searchInputContainer: {
		width: Platform.OS === 'ios' ? HORIZONTAL_DIMENS._438 : HORIZONTAL_DIMENS._375
	},
	searchInput: {
		paddingRight: HORIZONTAL_DIMENS._51
	},
	confirmBtn: {
		alignItems: 'center',
		backgroundColor: colors.secondary,
		borderRadius: 30,
		flexDirection: 'row',
		justifyContent: 'center',
		marginLeft: 10,
		paddingLeft: 36,
		paddingRight: 16,
		paddingVertical: 14
	},
	confirmBtnDisabled: {
		backgroundColor: colors.grey300
	},
	loading: {
		opacity: 0.6,
		paddingLeft: 80,
		//width: '20%',
		paddingRight: 80
	},
	confirmText: {
		color: colors.white,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		textTransform: 'uppercase',
		marginRight: 18
	},
	optionBtn: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 22,
		justifyContent: 'center',
		height: 44,
		width: 44,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 0
		},
		shadowOpacity: 0.08,
		shadowRadius: 1,
		elevation: 1
	},
	menuAnchorStyle: {
		opacity: 0
	},
	menuTrigger: {
		marginLeft: 10
	},
	menuOption: {
		alignItems: 'center',
		flexDirection: 'row'
	},
	menuOptionText: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._16,
		paddingLeft: 12,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	}
});

export { CartTableActions };
