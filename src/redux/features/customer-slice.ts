import AsyncStorage from '@react-native-async-storage/async-storage';
import { createSlice } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import i18n from '../../locales/i18n';
import { showDangerMessage, showSuccessMessage } from '../../utils/functions';
import { addCustomer, customerNumberVerify, getAllCustomers, getAllSalesPerson, getCustomerCartCount, getCustomerDetails, getExistsCustomers, getPriceList, updateCustomer } from '../apis/customer';

interface ObjectAny {
	[key: string]: any;
}

interface CustomerState {
	customers: Array<any>;
	loading: boolean;
	updatingCustomer: boolean;
	loadingCustomerInfo: boolean;
	activeCustomer: ObjectAny | null;
	priceList: Array<any>;
	salesPersons: Array<any>;
	customerDetails: any;
	selectedCustomer: {
		id: string;
		info: any;
	};
	cartCount: number;
	verifyingOTP: boolean;
	searchKey: string;
}

export const customerInitialState: CustomerState = {
	customers: [],
	loading: false,
	updatingCustomer: false,
	loadingCustomerInfo: false,
	activeCustomer: null,
	priceList: [],
	salesPersons: [],
	customerDetails: {},
	selectedCustomer: { id: '', info: {} },
	cartCount: 0,
	verifyingOTP: false,
	searchKey: ''
};

const customerSlice = createSlice({
	name: 'customer',
	initialState: customerInitialState,
	reducers: {
		/* -------------------------------------------------------------------------- */
		/*                       setCustomers is offline reducer                      */
		/* -------------------------------------------------------------------------- */
		setCustomers(state, action) {
			try {
				let customers = JSON.parse(JSON.stringify(action.payload));
				customers = customers.map((item: any) => {
					return {
						...item,
						key: item._id,
						value: item.customer_name,
						customer_name_insensitive: undefined
					};
				});
				state.customers = customers;

				// Update the active customer data if location or anything updates from CMS or any Device including same device Add/ Edit Customer.
				if (state.activeCustomer && state.activeCustomer?.is_verified) {
					const customer = customers.find((x: any) => x._id === state.activeCustomer?._id);
					if (customer && customer.is_verified) {
						state.activeCustomer = customer;
					}
				}

				// updating the state of Selected Customer in Customer Page to show selected cutomer and its details
				if (state.selectedCustomer.id !== '') {
					const customer = customers.find((x: any) => x._id === state.selectedCustomer.id);
					if (customer) {
						state.selectedCustomer = { id: customer?._id, info: {} };
						if (customers.length > 0) {
							state.customerDetails = customer;
						}
					}
				} else {
					state.selectedCustomer = { id: customers[0]?._id, info: {} };
					if (customers.length > 0) {
						state.customerDetails = customers[0];
					}
				}
			} catch {
			}
		},
		setSelectedCustomer(state, action) {
			if (action.payload === null) {
				state.cartCount = 0;
			}
			state.activeCustomer = action.payload;
		},
		/* -------------------------------------------------------------------------- */
		/*        Currently setSelectedCustomerFromList is used in offline only       */
		/* -------------------------------------------------------------------------- */
		setSelectedCustomerFromList(state, action) {
			state.selectedCustomer = { id: action.payload._id, info: {} };
			state.customerDetails = action.payload;
		},
		clearActiveCustomer(state) {
			state.activeCustomer = null;
		},
		setCustomerGPSCoordinates(state, action) {
			const customerRoleId = action.payload.customerRoleId;
			const customers = state.customers;
			const customerIndex = customers.findIndex(x => x._id === customerRoleId);
			if (customerIndex !== -1) {
				const customer = state.customers[customerIndex];
				state.customers[customerIndex] = {
					...customer,
					gps_coordinates: {
						longitude: action.payload?.longitude,
						latitude: action.payload?.latitude
					}
				};
			}
		},
		setCustomerVerified(state, action) {
			const customers = state.customers;
			const customerIndex = customers.findIndex(x => x._id === action.payload);
			if (customerIndex !== -1) {
				const customer = state.customers[customerIndex];
				state.customers[customerIndex] = {
					...customer,
					is_verified: true
				};
			}
		},
		/* -------------------------------------------------------------------------- */
		/*                       setPriceList is offline reducer                      */
		/* -------------------------------------------------------------------------- */
		setPriceList(state, action) {
			const priceList = action.payload.map((x: any) => {
				return {
					name: i18n.language === 'en' ? x.price_name : x.secondary_language_price_name,
					value: x._id.toString(),
					is_default: x.is_default
				};
			});
			state.priceList = priceList;
		},
		setSearchKey(state, action) {
			state.searchKey = action.payload;
		},
		setActiveCustomerGeocoder(state, action) {
			const geocoder = action.payload;
			if (state.activeCustomer !== null) state.activeCustomer.gps_coordinates = geocoder;
		},
		/* -------------------------------------------------------------------------- */
		/*      used for tablet offline place order if shipping number is not there  */
		/* -------------------------------------------------------------------------- */
		setActiveCustomerShippingNumberDetails(state, action) {
			const updatedActiveCusotmer = {
				...state.activeCustomer,
				shipping_country_code: action.payload.shippingCountryCode,
				shipping_mobile_number: action.payload.shippingMobileNumber
			};
			state.activeCustomer = updatedActiveCusotmer;
		}
	},
	extraReducers: (builder) => {
		builder.addCase(getAllCustomers.pending, (state) => {
			state.loading = true;
		});
		builder.addCase(getAllCustomers.fulfilled, (state, action: any) => {
			let data = action.payload.data;
			data = data.map((item: any) => {
				return {
					...item,
					key: item._id,
					value: item.customer_name,
					customer_name_insensitive: undefined
				};
			});
			state.customers = data;
			state.selectedCustomer = {
				id: state.selectedCustomer.id?.length > 0 ? state.selectedCustomer.id : data[0]?._id,
				info: {}
			};
			state.loading = false;
		});
		builder.addCase(getAllCustomers.rejected, (state) => {
			state.loading = false;
		});
		builder.addCase(customerNumberVerify.pending, (state) => {
			state.verifyingOTP = true;
		});
		builder.addCase(customerNumberVerify.fulfilled, (state, _action) => {
			state.verifyingOTP = false;
		});
		builder.addCase(customerNumberVerify.rejected, (state, _action) => {
			state.verifyingOTP = false;
		});
		builder.addCase(getPriceList.fulfilled, (state, action) => {
			const priceList = action.payload.data.list.map((x: any) => {
				return {
					name: i18n.language === 'en' ? x.price_name : x.secondary_language_price_name,
					value: x._id,
					is_default: x.is_default
				};
			});
			state.priceList = priceList;
		});
		builder.addCase(getAllSalesPerson.fulfilled, (state, action) => {
			const salesPersons = action.payload.data.map((x: any) => {
				return {
					name: x.user_id.first_name + ' ' + x.user_id.last_name,
					value: x._id
				};
			});
			state.salesPersons = salesPersons;
			//console.log('state.salesPerson', state.salesPersons);
		});
		builder.addCase(getExistsCustomers.pending, (state) => {
			state.loading = true;
		});
		builder.addCase(getExistsCustomers.fulfilled, (state) => {
			state.loading = false;
		});
		builder.addCase(getExistsCustomers.rejected, (state) => {
			state.loading = false;
		});
		builder.addCase(addCustomer.pending, (state) => {
			state.loading = true;
		});
		builder.addCase(addCustomer.fulfilled, (state) => {
			state.loading = false;
		});
		builder.addCase(addCustomer.rejected, (state, action) => {
			state.loading = false;
			showDangerMessage(i18n.t(action.payload.message));
		});
		builder.addCase(getCustomerDetails.pending, (state) => {
			state.loadingCustomerInfo = true;
		});
		builder.addCase(getCustomerDetails.fulfilled, (state, action) => {
			// console.log('customerDetails', action.payload.data);
			state.customerDetails = action.payload.data;
			state.loadingCustomerInfo = false;
		});
		builder.addCase(getCustomerDetails.rejected, (state, _action) => {
			state.loadingCustomerInfo = false;
		});
		builder.addCase(updateCustomer.pending, (state) => {
			state.updatingCustomer = true;
		});
		builder.addCase(updateCustomer.fulfilled, (state, action) => {
			state.updatingCustomer = false;
			showSuccessMessage(i18n.t(action.payload.message), 'navbar');
		});
		builder.addCase(updateCustomer.rejected, (state, action) => {
			state.updatingCustomer = false;
			showDangerMessage(i18n.t(action.payload.message), 'navbar');
		});
		builder.addCase(getCustomerCartCount.fulfilled, (state, action) => {
			const cartItemCount = action.payload?.data?.count;
			state.cartCount = cartItemCount ? cartItemCount : 0;
		});
	}
});

const persistConfig = {
	key: 'customer',
	storage: AsyncStorage,
	whitelist: ['activeCustomer', 'priceList'] // Persist some values
};

export const {
	setSelectedCustomer,
	setSelectedCustomerFromList,
	setCustomers,
	clearActiveCustomer,
	setCustomerGPSCoordinates,
	setCustomerVerified,
	setPriceList,
	setSearchKey,
	setActiveCustomerGeocoder,
	setActiveCustomerShippingNumberDetails
} = customerSlice.actions;
export default persistReducer(persistConfig, customerSlice.reducer);